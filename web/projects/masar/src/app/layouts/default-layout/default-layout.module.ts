import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DefaultLayoutComponent } from './default-layout.component';
import { RouterModule } from '@angular/router';
import { SharedModule } from '@masar/shared/shared.module';
import { TranslateModule } from '@ngx-translate/core';
import { HeaderComponent } from './components/header/header.component';
import { FooterComponent } from './components/footer/footer.component';
import { PrivacyPolicyAlertComponent } from './components/privacy-policy-alert/privacy-policy-alert.component';
import { DropdownAppsComponent } from './components/dropdown-apps/dropdown-apps.component';
import { NotificationListComponent } from './components/notification-list/notification-list.component';
import { FormatDistancePipeModule } from 'ngx-date-fns';
import { SweetAlert2Module } from '@sweetalert2/ngx-sweetalert2';
import { EasterModule } from '@masar/features/easter/easter.module';
import { DefaultLayoutRouting } from './default-layout.routing';
import { UnderConstructionComponent } from './components/under-construction/under-construction.component';
import { NotificationHeaderComponent } from './components/notification-header/notification-header.component';
import { UserCardComponent } from '@masar/layouts/default-layout/components/user-card/user-card.component';
import { LogoComponent } from '@masar/layouts/default-layout/components/logo/logo.component';
import { DynamicEntityModule } from 'projects/dynamic-entity/src/public-api';

@NgModule({
    declarations: [
        DefaultLayoutComponent,
        UnderConstructionComponent,
        HeaderComponent,
        FooterComponent,
        PrivacyPolicyAlertComponent,
        DropdownAppsComponent,
        NotificationListComponent,
        NotificationHeaderComponent,
        UserCardComponent,
        LogoComponent,
    ],
    imports: [
        CommonModule,
        RouterModule,
        SharedModule,
        TranslateModule,
        FormatDistancePipeModule,
        SweetAlert2Module,
        EasterModule,
        DefaultLayoutRouting,
        DynamicEntityModule,
    ],
})
export class DefaultLayoutModule {}
