<div class="flex h-full w-full bg-gray-200">
    <aside
        *ngIf="(defaultLayoutService.isSidebarHidden$ | async) === false"
        [class.sidebar-collapse]="isSidebarCollapsed"
        class="relative hidden p-2 pe-0 md:flex"
    >
        <div class="custom-shadow flex w-full flex-col rounded-md bg-primary">
            <div
                [class.bg-primary]="isSidebarCollapsed"
                class="flex items-center justify-center rounded-t-md border-b border-gray-200 bg-primary-700 py-1 text-white"
                style="height: 75px; min-height: 75px"
            >
                <app-logo [isSidebarCollapsed]="isSidebarCollapsed"></app-logo>
            </div>

            <app-sidebar
                [isSidebarCollapsed]="isSidebarCollapsed"
                [navItems]="navItems"
                class="block flex-grow overflow-auto text-sm text-white"
                id="sidebar-nav"
            ></app-sidebar>
        </div>
    </aside>
    <div class="relative flex flex-grow flex-col overflow-hidden">
        <app-notification-header></app-notification-header>
        <!-- HEADER -->
        <app-header
            (toggleSidebarCollapse)="isSidebarCollapsed = !isSidebarCollapsed"
            [isSidebarCollapsed]="isSidebarCollapsed"
        ></app-header>

        <div #contentWrapper class="flex flex-grow flex-col overflow-auto">
            <main class="flex-grow overflow-auto p-2 pb-0">
                <router-outlet></router-outlet>
            </main>

            <app-footer class="p-2 pt-0"></app-footer>
        </div>

        <!-- Security alert -->
        <app-privacy-alert
            class="absolute bottom-0 left-0 right-0 block"
        ></app-privacy-alert>
    </div>
</div>
