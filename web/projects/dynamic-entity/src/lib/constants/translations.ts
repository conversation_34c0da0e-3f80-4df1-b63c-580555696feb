export interface Translation {
  [key: string]: string;
}

export interface Translations {
  en: Translation;
  ar: Translation;
}

export const TRANSLATIONS: Translations = {
  en: {
    // Entity Names
    "entity.kpis": "KPIs",
    "entity.operations": "Operations",
    "entity.benchmarks": "Benchmarks",
    "entity.plans": "Plans",
    "entity.tasks": "Tasks",
    "entity.partners": "Partners",
    "entity.services": "Services",
    "entity.risks": "Risks",
    "entity.tournaments": "Awards",
    "entity.capabilities": "Capabilities",
    "entity.opportunities": "Improvement Opportunities",

    // General Terms
    "general.field": "field",
    "general.fields": "fields",
    "general.explore": "Explore",
    "general.back": "Back",
    "general.close": "Close",

    // Popup
    "popup.title": "Entities",
    "popup.close": "Close popup",

    // Empty State
    "empty.title": "No Entities Found",
    "empty.description": "There are no entities available at the moment.",

    // Details
    "details.title": "Entity Details",
    "details.description": "Entity details content will be implemented here.",
    "details.backToList": "Back to list",

    // Actions
    "action.openPopup": "Open popup",
  },
  ar: {
    // Entity Names
    "entity.kpis": "مؤشرات الاداء",
    "entity.operations": "العمليات",
    "entity.benchmarks": "المقارنات المرجعية",
    "entity.plans": "الخطط",
    "entity.tasks": "المهام",
    "entity.partners": "الشركاء",
    "entity.services": "الخدمات",
    "entity.risks": "المخاطر",
    "entity.tournaments": "الجوائز",
    "entity.capabilities": "القدرات",
    "entity.opportunities": "الفرص التحسينية",

    // General Terms
    "general.field": "حقل",
    "general.fields": "حقول",
    "general.explore": "استكشاف",
    "general.back": "رجوع",
    "general.close": "إغلاق",

    // Popup
    "popup.title": "الوحدات",
    "popup.close": "إغلاق النافذة",

    // Empty State
    "empty.title": "لم يتم العثور على وحدات",
    "empty.description": "لا توجد وحدات متاحة في الوقت الحالي.",

    // Details
    "details.title": "تفاصيل الوحدة",
    "details.description": "سيتم تنفيذ محتوى تفاصيل الوحدة هنا.",
    "details.backToList": "العودة إلى القائمة",

    // Actions
    "action.openPopup": "فتح النافذة",
  },
};

export type SupportedLanguage = keyof Translations;
