export interface Translation {
  [key: string]: string;
}

export interface Translations {
  en: Translation;
  ar: Translation;
}

export const TRANSLATIONS: Translations = {
  en: {
    // Entity Names
    'entity.kpis': 'KPIs',
    'entity.operations': 'Operations',
    'entity.benchmarks': 'Benchmarks',
    'entity.plans': 'Plans',
    'entity.tasks': 'Tasks',
    'entity.partners': 'Partners',
    'entity.services': 'Services',
    'entity.risks': 'Risks',
    'entity.tournaments': 'Tournaments',
    'entity.capabilities': 'Capabilities',
    'entity.opportunities': 'Opportunities',

    // General Terms
    'general.field': 'field',
    'general.fields': 'fields',
    'general.explore': 'Explore',
    'general.back': 'Back',
    'general.close': 'Close',

    // Popup
    'popup.title': 'Dynamic Entities',
    'popup.close': 'Close popup',

    // Empty State
    'empty.title': 'No Entities Found',
    'empty.description': 'There are no entities available at the moment.',

    // Details
    'details.title': 'Entity Details',
    'details.description': 'Entity details content will be implemented here.',
    'details.backToList': 'Back to list',

    // Actions
    'action.openPopup': 'Open popup',
  },
  ar: {
    // Entity Names
    'entity.kpis': 'مؤشرات الأداء الرئيسية',
    'entity.operations': 'العمليات',
    'entity.benchmarks': 'المعايير المرجعية',
    'entity.plans': 'الخطط',
    'entity.tasks': 'المهام',
    'entity.partners': 'الشركاء',
    'entity.services': 'الخدمات',
    'entity.risks': 'المخاطر',
    'entity.tournaments': 'البطولات',
    'entity.capabilities': 'القدرات',
    'entity.opportunities': 'الفرص',

    // General Terms
    'general.field': 'حقل',
    'general.fields': 'حقول',
    'general.explore': 'استكشاف',
    'general.back': 'رجوع',
    'general.close': 'إغلاق',

    // Popup
    'popup.title': 'الكيانات الديناميكية',
    'popup.close': 'إغلاق النافذة',

    // Empty State
    'empty.title': 'لم يتم العثور على كيانات',
    'empty.description': 'لا توجد كيانات متاحة في الوقت الحالي.',

    // Details
    'details.title': 'تفاصيل الكيان',
    'details.description': 'سيتم تنفيذ محتوى تفاصيل الكيان هنا.',
    'details.backToList': 'العودة إلى القائمة',

    // Actions
    'action.openPopup': 'فتح النافذة',
  },
};

export type SupportedLanguage = keyof Translations;

export class TranslationService {
  private currentLanguage: SupportedLanguage = 'en';

  public setLanguage(language: SupportedLanguage): void {
    this.currentLanguage = language;
  }

  public getCurrentLanguage(): SupportedLanguage {
    return this.currentLanguage;
  }

  public translate(key: string): string {
    const translation = TRANSLATIONS[this.currentLanguage][key];
    return translation || key;
  }

  public translateWithParams(key: string, params: { [key: string]: string | number }): string {
    let translation = this.translate(key);
    
    Object.keys(params).forEach(param => {
      translation = translation.replace(`{{${param}}}`, String(params[param]));
    });
    
    return translation;
  }

  public getSupportedLanguages(): SupportedLanguage[] {
    return Object.keys(TRANSLATIONS) as SupportedLanguage[];
  }
}

// Singleton instance
export const translationService = new TranslationService();
