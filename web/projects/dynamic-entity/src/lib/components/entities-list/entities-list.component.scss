// Entities Grid
.entities-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 1.5rem;
  padding: 1rem 0;
}

// Entity Card Base
.entity-card {
  position: relative;
  height: 180px;
  border-radius: 20px;
  cursor: pointer;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);

  &:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);

    .icon-glow {
      opacity: 1;
      transform: scale(1.2);
    }

    .hover-overlay {
      opacity: 1;
    }

    .entity-icon {
      transform: scale(1.1);
    }
  }

  &:active {
    transform: translateY(-4px) scale(1.01);
  }
}

// Card Background Patterns
.card-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0.1;
  background-size: 30px 30px;
  background-repeat: repeat;
}

// Different gradient themes for cards
.entity-card-0 {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);

  .card-background {
    background-image: radial-gradient(
      circle,
      rgba(255, 255, 255, 0.3) 1px,
      transparent 1px
    );
  }
}

.entity-card-1 {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);

  .card-background {
    background-image: linear-gradient(
        45deg,
        rgba(255, 255, 255, 0.1) 25%,
        transparent 25%
      ),
      linear-gradient(-45deg, rgba(255, 255, 255, 0.1) 25%, transparent 25%);
  }
}

.entity-card-2 {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);

  .card-background {
    background-image: repeating-linear-gradient(
      45deg,
      rgba(255, 255, 255, 0.1),
      rgba(255, 255, 255, 0.1) 2px,
      transparent 2px,
      transparent 10px
    );
  }
}

.entity-card-3 {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);

  .card-background {
    background-image: radial-gradient(
        circle at 25% 25%,
        rgba(255, 255, 255, 0.2) 2px,
        transparent 2px
      ),
      radial-gradient(
        circle at 75% 75%,
        rgba(255, 255, 255, 0.2) 2px,
        transparent 2px
      );
  }
}

// Card Content
.card-content {
  position: relative;
  height: 100%;
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  z-index: 2;
}

// Entity Icon Container
.entity-icon-container {
  position: relative;
  align-self: flex-start;
}

.entity-icon {
  width: 60px;
  height: 60px;
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);

  display: flex;
  align-items: center;
  justify-content: center;

  font-size: 1.5rem;
  color: white;
  transition: all 0.3s ease;

  i {
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
  }
}

.icon-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: radial-gradient(
    circle,
    rgba(255, 255, 255, 0.4) 0%,
    transparent 70%
  );
  transform: translate(-50%, -50%) scale(0.8);
  opacity: 0;
  transition: all 0.3s ease;
  pointer-events: none;
}

// Entity Info
.entity-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
}

.entity-name {
  margin: 0 0 0.5rem 0;
  font-size: 1.5rem;
  font-weight: 700;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  letter-spacing: -0.025em;
}

.entity-fields {
  display: flex;
  align-items: baseline;
  gap: 0.25rem;
}

.fields-count {
  font-size: 1.125rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.fields-label {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.7);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

// Hover Overlay
.hover-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(2px);

  display: flex;
  align-items: center;
  justify-content: center;

  opacity: 0;
  transition: all 0.3s ease;
  z-index: 3;
}

.hover-content {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 50px;
  color: white;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  transform: translateY(10px);
  transition: all 0.3s ease;
}

.entity-card:hover .hover-content {
  transform: translateY(0);
}

.hover-arrow {
  font-size: 0.875rem;
  transition: transform 0.2s ease;
}

.hover-content:hover .hover-arrow {
  transform: translateX(2px);
}

.hover-text {
  font-size: 0.875rem;
}

// Empty State
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
}

.empty-icon {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
  color: #9ca3af;

  display: flex;
  align-items: center;
  justify-content: center;

  font-size: 2rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.empty-title {
  margin: 0 0 0.75rem 0;
  font-size: 1.5rem;
  font-weight: 700;
  color: #1f2937;
}

.empty-description {
  margin: 0;
  font-size: 1rem;
  color: #6b7280;
  max-width: 400px;
  line-height: 1.6;
}

// Responsive Design
@media (max-width: 768px) {
  .entities-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
    padding: 0.5rem 0;
  }

  .entity-card {
    height: 160px;
  }

  .card-content {
    padding: 1.25rem;
  }

  .entity-icon {
    width: 50px;
    height: 50px;
    font-size: 1.25rem;
  }

  .entity-name {
    font-size: 1.25rem;
  }

  .fields-count {
    font-size: 1rem;
  }

  .fields-label {
    font-size: 0.8125rem;
  }

  .hover-content {
    padding: 0.5rem 1rem;
    font-size: 0.8125rem;
  }
}

@media (max-width: 480px) {
  .entities-grid {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .entity-card {
    height: 140px;
  }

  .card-content {
    padding: 1rem;
  }

  .entity-icon {
    width: 44px;
    height: 44px;
    font-size: 1.125rem;
  }

  .entity-name {
    font-size: 1.125rem;
  }
}
