// Entities Grid
.entities-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1rem;
  padding: 0.5rem 0;
}

// Entity Card
.entity-card {
  display: flex;
  align-items: center;
  padding: 1.25rem;
  
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    border-color: #3b82f6;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
    transform: translateY(-2px);
  }
  
  &:active {
    transform: translateY(0);
  }
}

// Entity Icon
.entity-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  
  display: flex;
  align-items: center;
  justify-content: center;
  
  font-size: 1.25rem;
  flex-shrink: 0;
  margin-right: 1rem;
}

// Entity Info
.entity-info {
  flex: 1;
  min-width: 0; // Allow text truncation
}

.entity-name {
  margin: 0 0 0.25rem 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  
  // Truncate long names
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.entity-fields {
  margin: 0;
  font-size: 0.875rem;
  color: #6b7280;
}

// Entity Arrow
.entity-arrow {
  color: #9ca3af;
  font-size: 0.875rem;
  margin-left: 0.75rem;
  transition: all 0.2s ease;
}

.entity-card:hover .entity-arrow {
  color: #3b82f6;
  transform: translateX(2px);
}

// Empty State
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 1rem;
  text-align: center;
}

.empty-icon {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  
  background: #f3f4f6;
  color: #9ca3af;
  
  display: flex;
  align-items: center;
  justify-content: center;
  
  font-size: 1.5rem;
  margin-bottom: 1rem;
}

.empty-title {
  margin: 0 0 0.5rem 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
}

.empty-description {
  margin: 0;
  font-size: 0.875rem;
  color: #6b7280;
  max-width: 300px;
}

// Responsive Design
@media (max-width: 768px) {
  .entities-grid {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }
  
  .entity-card {
    padding: 1rem;
  }
  
  .entity-icon {
    width: 40px;
    height: 40px;
    font-size: 1rem;
    margin-right: 0.75rem;
  }
  
  .entity-name {
    font-size: 1rem;
  }
  
  .entity-fields {
    font-size: 0.8125rem;
  }
}
