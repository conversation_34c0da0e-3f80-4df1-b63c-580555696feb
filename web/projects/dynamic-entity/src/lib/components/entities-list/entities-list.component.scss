// Entities Grid
.entities-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.25rem;
  padding: 1rem 0;
}

// Entity Card Base
.entity-card {
  position: relative;
  height: 160px;
  border-radius: 16px;
  cursor: pointer;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08), 0 1px 3px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(255, 255, 255, 0.1);

  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12), 0 4px 10px rgba(0, 0, 0, 0.08);
    border-color: rgba(255, 255, 255, 0.2);

    .hover-overlay {
      opacity: 1;
    }

    .entity-icon {
      transform: scale(1.05);
    }
  }

  &:active {
    transform: translateY(-1px);
  }
}

// Card Background Patterns
.card-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0.1;
  background-size: 30px 30px;
  background-repeat: repeat;
}

// Card Content
.card-content {
  position: relative;
  height: 100%;
  padding: 1.25rem;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  z-index: 2;
}

// Entity Icon Container
.entity-icon-container {
  position: relative;
  align-self: flex-start;
}

.entity-icon {
  width: 52px;
  height: 52px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.25);
  transition: transform 0.2s ease;

  display: flex;
  align-items: center;
  justify-content: center;

  font-size: 1.25rem;
  color: white;

  i {
    filter: drop-shadow(0 1px 3px rgba(0, 0, 0, 0.3));
  }
}

// Entity Info
.entity-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
}

.entity-name {
  margin: 0 0 0.375rem 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: white;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.4);
  letter-spacing: -0.01em;
  line-height: 1.2;
}

.entity-fields {
  display: flex;
  align-items: baseline;
  gap: 0.25rem;
}

.fields-count {
  font-size: 1rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.95);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.fields-label {
  font-size: 0.8125rem;
  color: rgba(255, 255, 255, 0.8);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  font-weight: 500;
}

// Hover Overlay
.hover-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.25);

  display: flex;
  align-items: center;
  justify-content: center;

  opacity: 0;
  transition: opacity 0.2s ease;
  z-index: 2;
}

.hover-content {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.625rem 1.25rem;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 24px;
  color: white;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.4);
  font-size: 0.8125rem;
  letter-spacing: 0.025em;
}

.hover-arrow {
  font-size: 0.75rem;
  transition: transform 0.2s ease;
}

.hover-content:hover .hover-arrow {
  transform: translateX(2px);
}

.hover-text {
  font-size: 0.8125rem;
}

// Empty State
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
}

.empty-icon {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
  color: #9ca3af;

  display: flex;
  align-items: center;
  justify-content: center;

  font-size: 2rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.empty-title {
  margin: 0 0 0.75rem 0;
  font-size: 1.5rem;
  font-weight: 700;
  color: #1f2937;
}

.empty-description {
  margin: 0;
  font-size: 1rem;
  color: #6b7280;
  max-width: 400px;
  line-height: 1.6;
}

// Responsive Design
@media (max-width: 768px) {
  .entities-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 1rem;
    padding: 0.75rem 0;
  }

  .entity-card {
    height: 150px;
  }

  .card-content {
    padding: 1rem;
  }

  .entity-icon {
    width: 48px;
    height: 48px;
    font-size: 1.125rem;
  }

  .entity-name {
    font-size: 1.125rem;
  }

  .fields-count {
    font-size: 0.9375rem;
  }

  .fields-label {
    font-size: 0.75rem;
  }

  .hover-content {
    padding: 0.5rem 1rem;
    font-size: 0.75rem;
  }
}

@media (max-width: 480px) {
  .entities-grid {
    grid-template-columns: 1fr;
    gap: 0.875rem;
  }

  .entity-card {
    height: 140px;
  }

  .card-content {
    padding: 1rem;
  }

  .entity-icon {
    width: 44px;
    height: 44px;
    font-size: 1rem;
  }

  .entity-name {
    font-size: 1rem;
  }

  .fields-count {
    font-size: 0.875rem;
  }

  .fields-label {
    font-size: 0.75rem;
  }
}
