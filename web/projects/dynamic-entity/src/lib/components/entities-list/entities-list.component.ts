import { Component, EventEmitter, Input, OnInit, Output } from "@angular/core";
import { TRANSLATIONS, SupportedLanguage } from "../../constants/translations";

export interface Entity {
  id: string;
  name: string;
  nameKey: string;
  icon: string;
  fieldsCount: number;
  background: string;
}

@Component({
  selector: "lib-entities-list",
  templateUrl: "./entities-list.component.html",
  styleUrls: ["./entities-list.component.scss"],
})
export class EntitiesListComponent implements OnInit {
  @Input() selectedLanguage: SupportedLanguage = "en";
  @Output() entitySelected = new EventEmitter<Entity>();

  public entities: Entity[] = [];

  public get translations() {
    return TRANSLATIONS[this.selectedLanguage];
  }

  public ngOnInit(): void {
    // Business entities with appropriate icons and field counts
    this.entities = [
      {
        id: "1",
        name: "KPIs",
        nameKey: "entity.kpis",
        icon: "fas fa-chart-line",
        fieldsCount: 12,
        background: "linear-gradient(135deg, #1e3a8a 0%, #3730a3 100%)",
      },
      {
        id: "2",
        name: "Operations",
        nameKey: "entity.operations",
        icon: "fas fa-cogs",
        fieldsCount: 18,
        background: "linear-gradient(135deg, #059669 0%, #047857 100%)",
      },
      {
        id: "3",
        name: "Benchmarks",
        nameKey: "entity.benchmarks",
        icon: "fas fa-balance-scale",
        fieldsCount: 10,
        background: "linear-gradient(135deg, #0891b2 0%, #0e7490 100%)",
      },
      {
        id: "4",
        name: "Plans",
        nameKey: "entity.plans",
        icon: "fas fa-project-diagram",
        fieldsCount: 15,
        background: "linear-gradient(135deg, #7c3aed 0%, #6d28d9 100%)",
      },
      {
        id: "5",
        name: "Tasks",
        nameKey: "entity.tasks",
        icon: "fas fa-tasks",
        fieldsCount: 8,
        background: "linear-gradient(135deg, #dc2626 0%, #b91c1c 100%)",
      },
      {
        id: "6",
        name: "Partners",
        nameKey: "entity.partners",
        icon: "fas fa-handshake",
        fieldsCount: 14,
        background: "linear-gradient(135deg, #ea580c 0%, #c2410c 100%)",
      },
      {
        id: "7",
        name: "Services",
        nameKey: "entity.services",
        icon: "fas fa-concierge-bell",
        fieldsCount: 11,
        background: "linear-gradient(135deg, #0f766e 0%, #134e4a 100%)",
      },
      {
        id: "8",
        name: "Risks",
        nameKey: "entity.risks",
        icon: "fas fa-exclamation-triangle",
        fieldsCount: 9,
        background: "linear-gradient(135deg, #be123c 0%, #9f1239 100%)",
      },
      {
        id: "9",
        name: "Tournaments",
        nameKey: "entity.tournaments",
        icon: "fas fa-trophy",
        fieldsCount: 13,
        background: "linear-gradient(135deg, #a16207 0%, #92400e 100%)",
      },
      {
        id: "10",
        name: "Capabilities",
        nameKey: "entity.capabilities",
        icon: "fas fa-rocket",
        fieldsCount: 16,
        background: "linear-gradient(135deg, #4338ca 0%, #3730a3 100%)",
      },
      {
        id: "11",
        name: "Opportunities",
        nameKey: "entity.opportunities",
        icon: "fas fa-lightbulb",
        fieldsCount: 7,
        background: "linear-gradient(135deg, #065f46 0%, #064e3b 100%)",
      },
    ];
  }

  public onEntityClick(entity: Entity): void {
    this.entitySelected.emit(entity);
  }
}
