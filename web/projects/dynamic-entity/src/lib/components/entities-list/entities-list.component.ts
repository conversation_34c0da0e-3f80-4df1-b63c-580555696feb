import { Component, EventEmitter, OnInit, Output } from "@angular/core";

export interface Entity {
  id: string;
  name: string;
  icon: string;
  fieldsCount: number;
  background: string;
}

@Component({
  selector: "lib-entities-list",
  templateUrl: "./entities-list.component.html",
  styleUrls: ["./entities-list.component.scss"],
})
export class EntitiesListComponent implements OnInit {
  @Output() entitySelected = new EventEmitter<Entity>();

  public entities: Entity[] = [];

  public ngOnInit(): void {
    // Business entities with appropriate icons and field counts
    this.entities = [
      {
        id: "1",
        name: "KPIs",
        icon: "fas fa-chart-line",
        fieldsCount: 12,
        background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
      },
      {
        id: "2",
        name: "Operations",
        icon: "fas fa-cogs",
        fieldsCount: 18,
        background: "linear-gradient(135deg, #f093fb 0%, #f5576c 100%)",
      },
      {
        id: "3",
        name: "Benchmarks",
        icon: "fas fa-balance-scale",
        fieldsCount: 10,
        background: "linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)",
      },
      {
        id: "4",
        name: "Plans",
        icon: "fas fa-project-diagram",
        fieldsCount: 15,
        background: "linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)",
      },
      {
        id: "5",
        name: "Tasks",
        icon: "fas fa-tasks",
        fieldsCount: 8,
        background: "linear-gradient(135deg, #fa709a 0%, #fee140 100%)",
      },
      {
        id: "6",
        name: "Partners",
        icon: "fas fa-handshake",
        fieldsCount: 14,
        background: "linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)",
      },
      {
        id: "7",
        name: "Services",
        icon: "fas fa-concierge-bell",
        fieldsCount: 11,
        background: "linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)",
      },
      {
        id: "8",
        name: "Risks",
        icon: "fas fa-exclamation-triangle",
        fieldsCount: 9,
        background: "linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)",
      },
      {
        id: "9",
        name: "Tournaments",
        icon: "fas fa-trophy",
        fieldsCount: 13,
        background: "linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%)",
      },
      {
        id: "10",
        name: "Capabilities",
        icon: "fas fa-rocket",
        fieldsCount: 16,
        background: "linear-gradient(135deg, #fad0c4 0%, #ffd1ff 100%)",
      },
      {
        id: "11",
        name: "Opportunities",
        icon: "fas fa-lightbulb",
        fieldsCount: 7,
        background: "linear-gradient(135deg, #a3bded 0%, #6991c7 100%)",
      },
    ];
  }

  public onEntityClick(entity: Entity): void {
    this.entitySelected.emit(entity);
  }
}
