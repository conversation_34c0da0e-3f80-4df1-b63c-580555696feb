import { Component, EventEmitter, OnInit, Output } from '@angular/core';

export interface Entity {
  id: string;
  name: string;
  icon: string;
  fieldsCount: number;
}

@Component({
  selector: 'lib-entities-list',
  templateUrl: './entities-list.component.html',
  styleUrls: ['./entities-list.component.scss'],
})
export class EntitiesListComponent implements OnInit {
  @Output() entitySelected = new EventEmitter<Entity>();

  public entities: Entity[] = [];

  public ngOnInit(): void {
    // Mock data for demonstration
    this.entities = [
      {
        id: '1',
        name: 'Users',
        icon: 'fas fa-users',
        fieldsCount: 8,
      },
      {
        id: '2',
        name: 'Products',
        icon: 'fas fa-box',
        fieldsCount: 12,
      },
      {
        id: '3',
        name: 'Orders',
        icon: 'fas fa-shopping-cart',
        fieldsCount: 15,
      },
      {
        id: '4',
        name: 'Categories',
        icon: 'fas fa-tags',
        fieldsCount: 6,
      },
      {
        id: '5',
        name: 'Reviews',
        icon: 'fas fa-star',
        fieldsCount: 9,
      },
      {
        id: '6',
        name: 'Inventory',
        icon: 'fas fa-warehouse',
        fieldsCount: 11,
      },
    ];
  }

  public onEntityClick(entity: Entity): void {
    this.entitySelected.emit(entity);
  }
}
