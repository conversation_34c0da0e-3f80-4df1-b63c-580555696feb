import { Component, EventEmitter, OnInit, Output } from "@angular/core";

export interface Entity {
  id: string;
  name: string;
  icon: string;
  fieldsCount: number;
}

@Component({
  selector: "lib-entities-list",
  templateUrl: "./entities-list.component.html",
  styleUrls: ["./entities-list.component.scss"],
})
export class EntitiesListComponent implements OnInit {
  @Output() entitySelected = new EventEmitter<Entity>();

  public entities: Entity[] = [];

  public ngOnInit(): void {
    // Business entities with appropriate icons and field counts
    this.entities = [
      {
        id: "1",
        name: "KPIs",
        icon: "fas fa-chart-line",
        fieldsCount: 12,
      },
      {
        id: "2",
        name: "Operations",
        icon: "fas fa-cogs",
        fieldsCount: 18,
      },
      {
        id: "3",
        name: "Benchmarks",
        icon: "fas fa-balance-scale",
        fieldsCount: 10,
      },
      {
        id: "4",
        name: "Plans",
        icon: "fas fa-project-diagram",
        fieldsCount: 15,
      },
      {
        id: "5",
        name: "Tasks",
        icon: "fas fa-tasks",
        fieldsCount: 8,
      },
      {
        id: "6",
        name: "Partners",
        icon: "fas fa-handshake",
        fieldsCount: 14,
      },
      {
        id: "7",
        name: "Services",
        icon: "fas fa-concierge-bell",
        fieldsCount: 11,
      },
      {
        id: "8",
        name: "Risks",
        icon: "fas fa-exclamation-triangle",
        fieldsCount: 9,
      },
      {
        id: "9",
        name: "Tournaments",
        icon: "fas fa-trophy",
        fieldsCount: 13,
      },
      {
        id: "10",
        name: "Capabilities",
        icon: "fas fa-rocket",
        fieldsCount: 16,
      },
      {
        id: "11",
        name: "Opportunities",
        icon: "fas fa-lightbulb",
        fieldsCount: 7,
      },
    ];
  }

  public onEntityClick(entity: Entity): void {
    this.entitySelected.emit(entity);
  }
}
