<!-- Entities Grid -->
<div class="entities-grid">
  <div
    *ngFor="let entity of entities"
    class="entity-card"
    (click)="onEntityClick(entity)"
  >
    <!-- Entity Icon -->
    <div class="entity-icon">
      <i [class]="entity.icon"></i>
    </div>

    <!-- Entity Info -->
    <div class="entity-info">
      <h3 class="entity-name">{{ entity.name }}</h3>
      <p class="entity-fields">
        {{ entity.fieldsCount }} 
        {{ entity.fieldsCount === 1 ? 'field' : 'fields' }}
      </p>
    </div>

    <!-- Arrow Icon -->
    <div class="entity-arrow">
      <i class="fas fa-chevron-right"></i>
    </div>
  </div>
</div>

<!-- Empty State (if no entities) -->
<div *ngIf="entities.length === 0" class="empty-state">
  <div class="empty-icon">
    <i class="fas fa-database"></i>
  </div>
  <h3 class="empty-title">No Entities Found</h3>
  <p class="empty-description">
    There are no entities available at the moment.
  </p>
</div>
