<!-- Entities Grid -->
<div class="entities-grid">
  <div *ngFor="let entity of entities" class="entity-card" [style.background]="entity.background" (click)="onEntityClick(entity)">
    <!-- Card Background Pattern -->
    <div class="card-background"></div>

    <!-- Card Content -->
    <div class="card-content">
      <!-- Entity Icon -->
      <div class="entity-icon-container">
        <div class="entity-icon">
          <i [class]="entity.icon"></i>
        </div>
        <div class="icon-glow"></div>
      </div>

      <!-- Entity Info -->
      <div class="entity-info">
        <h3 class="entity-name">{{ translationService.translate(entity.nameKey) }}</h3>
        <div class="entity-fields">
          <span class="fields-count">{{ entity.fieldsCount }}</span>
          <span class="fields-label">{{ entity.fieldsCount === 1 ? translationService.translate("general.field") : translationService.translate("general.fields") }}</span>
        </div>
      </div>

      <!-- Hover Effect -->
      <div class="hover-overlay">
        <div class="hover-content">
          <i class="fas fa-arrow-right hover-arrow"></i>
          <span class="hover-text">{{ translationService.translate("general.explore") }}</span>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Empty State (if no entities) -->
<div *ngIf="entities.length === 0" class="empty-state">
  <div class="empty-icon">
    <i class="fas fa-database"></i>
  </div>
  <h3 class="empty-title">{{ translationService.translate("empty.title") }}</h3>
  <p class="empty-description">{{ translationService.translate("empty.description") }}</p>
</div>
