import { Component } from '@angular/core';
import { animations } from './animations';

@Component({
  selector: 'lib-floating-button-popup',
  templateUrl: './floating-button-popup.component.html',
  styleUrls: ['./floating-button-popup.component.scss'],
  animations: animations,
})
export class FloatingButtonPopupComponent {
  public isPopupOpen = false;

  public togglePopup(): void {
    this.isPopupOpen = !this.isPopupOpen;
  }

  public closePopup(): void {
    this.isPopupOpen = false;
  }
}
