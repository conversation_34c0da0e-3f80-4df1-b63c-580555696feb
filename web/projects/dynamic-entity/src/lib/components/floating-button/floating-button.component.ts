import { Component, EventEmitter, Input, Output } from "@angular/core";
import { TRANSLATIONS, SupportedLanguage } from "../../constants/translations";

@Component({
  selector: "lib-floating-button",
  templateUrl: "./floating-button.component.html",
  styleUrls: ["./floating-button.component.scss"],
})
export class FloatingButtonComponent {
  @Input() selectedLanguage: SupportedLanguage = "en";
  @Output() buttonClick = new EventEmitter<void>();

  public get translations() {
    return TRANSLATIONS[this.selectedLanguage];
  }

  public onButtonClick(): void {
    this.buttonClick.emit();
  }
}
