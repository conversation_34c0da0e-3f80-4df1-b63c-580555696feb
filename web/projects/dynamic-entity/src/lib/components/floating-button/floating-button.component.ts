import { Component, EventEmitter, Output } from "@angular/core";
import { translationService } from "../../constants/translations";

@Component({
  selector: "lib-floating-button",
  templateUrl: "./floating-button.component.html",
  styleUrls: ["./floating-button.component.scss"],
})
export class FloatingButtonComponent {
  @Output() buttonClick = new EventEmitter<void>();
  public translationService = translationService;

  public onButtonClick(): void {
    this.buttonClick.emit();
  }
}
