// Floating <PERSON><PERSON> Styles
.floating-button {
  position: fixed;
  left: 2rem;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1000;
  
  width: 56px;
  height: 56px;
  border-radius: 50%;
  border: none;
  
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  
  display: flex;
  align-items: center;
  justify-content: center;
  
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  
  &:hover {
    transform: translateY(-50%) scale(1.1);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
  }
  
  &:active {
    transform: translateY(-50%) scale(0.95);
  }
  
  svg {
    transition: transform 0.2s ease;
  }
  
  &:hover svg {
    transform: rotate(90deg);
  }
}

// Responsive Design
@media (max-width: 768px) {
  .floating-button {
    left: 1rem;
    width: 48px;
    height: 48px;
  }
}
