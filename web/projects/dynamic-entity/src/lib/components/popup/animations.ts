import {
  animate,
  animateChild,
  group,
  query,
  style,
  transition,
  trigger,
} from "@angular/animations";

export const animations = [
  trigger("popupOverlay", [
    transition(":enter", [
      group([
        style({ opacity: 0.0 }),
        animate("200ms ease-in", style({ opacity: 1.0 })),
        query("@popupContent", animateChild()),
      ]),
    ]),

    transition(":leave", [animate("200ms ease-in", style({ opacity: 0.0 }))]),
  ]),

  trigger("popupContent", [
    transition(":enter", [
      style({
        transform: "translateY(100%)",
      }),
      animate(
        "300ms ease-in-out",
        style({
          transform: "translateY(0)",
        })
      ),
    ]),

    transition(":leave", [
      style({
        transform: "translateY(0)",
      }),
      animate(
        "250ms ease-in",
        style({
          transform: "translateY(100%)",
        })
      ),
    ]),
  ]),
];
