// Popup Overlay Styles
.popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1001;

  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(2px);

  display: flex;
  align-items: flex-end;
  justify-content: center;
}

// Popup Content Styles
.popup-content {
  width: 100%;
  height: 95%;

  background: white;
  border-radius: 20px 20px 0 0;

  display: flex;
  flex-direction: column;

  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.15);

  // Ensure content doesn't overflow
  overflow: hidden;
}

// Popup Header Styles
.popup-header {
  padding: 1.5rem 2rem 1rem;
  border-bottom: 1px solid #e5e7eb;

  display: flex;
  align-items: center;
  justify-content: space-between;

  flex-shrink: 0;
}

.popup-title {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
}

.popup-close-button {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: none;

  background: #f3f4f6;
  color: #6b7280;

  display: flex;
  align-items: center;
  justify-content: center;

  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: #e5e7eb;
    color: #374151;
  }

  &:active {
    transform: scale(0.95);
  }
}

// Popup Body Styles
.popup-body {
  flex: 1;
  padding: 2rem;

  overflow-y: auto;

  // Custom scrollbar
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;

    &:hover {
      background: #a8a8a8;
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .popup-content {
    border-radius: 16px 16px 0 0;
  }

  .popup-header {
    padding: 1rem 1.5rem 0.75rem;
  }

  .popup-title {
    font-size: 1.25rem;
  }

  .popup-body {
    padding: 1.5rem;
  }
}
