import { Component, EventEmitter, Input, Output } from "@angular/core";
import { animations } from "./animations";

@Component({
  selector: "lib-popup",
  templateUrl: "./popup.component.html",
  styleUrls: ["./popup.component.scss"],
  animations: animations,
})
export class PopupComponent {
  @Input() title = "Popup Title";
  @Output() closePopup = new EventEmitter<void>();

  public onClosePopup(): void {
    this.closePopup.emit();
  }

  public onOverlayClick(): void {
    this.onClosePopup();
  }

  public onContentClick(event: Event): void {
    event.stopPropagation();
  }
}
