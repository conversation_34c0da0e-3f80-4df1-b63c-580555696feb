import { Component, EventEmitter, Input, Output } from "@angular/core";
import { animations } from "./animations";
import { TRANSLATIONS, SupportedLanguage } from "../../constants/translations";

@Component({
  selector: "lib-popup",
  templateUrl: "./popup.component.html",
  styleUrls: ["./popup.component.scss"],
  animations: animations,
})
export class PopupComponent {
  @Input() title = "Popup Title";
  @Input() selectedLanguage: SupportedLanguage = "en";
  @Input() showBackButton = false;
  @Input() headerIcon = "";
  @Input() headerSubtitle = "";
  @Output() closePopup = new EventEmitter<void>();
  @Output() backClick = new EventEmitter<void>();

  public get translations() {
    return TRANSLATIONS[this.selectedLanguage];
  }

  public onClosePopup(): void {
    this.closePopup.emit();
  }

  public onBackClick(): void {
    this.backClick.emit();
  }

  public onOverlayClick(): void {
    this.onClosePopup();
  }

  public onContentClick(event: Event): void {
    event.stopPropagation();
  }
}
