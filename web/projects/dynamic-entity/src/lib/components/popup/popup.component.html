<!-- Popup Overlay -->
<div [@popupOverlay] (click)="onOverlayClick()" class="popup-overlay">
  <!-- Popup Content -->
  <div [@popupContent] (click)="onContentClick($event)" class="popup-content">
    <!-- Popup Header -->
    <div class="popup-header">
      <!-- Back Button (for details view) -->
      <button *ngIf="showBackButton" (click)="onBackClick()" class="popup-back-button" type="button" [attr.aria-label]="translate('details.backToList', selectedLanguage)">
        <i class="fas fa-arrow-left"></i>
      </button>

      <!-- Header Content -->
      <div class="popup-header-content">
        <!-- Entity Icon (for details view) -->
        <div *ngIf="headerIcon" class="popup-header-icon">
          <i [class]="headerIcon"></i>
        </div>

        <!-- Title and Subtitle -->
        <div class="popup-header-text">
          <h2 class="popup-title">{{ title }}</h2>
          <p *ngIf="headerSubtitle" class="popup-subtitle">{{ headerSubtitle }}</p>
        </div>
      </div>

      <!-- Close Button -->
      <button (click)="onClosePopup()" class="popup-close-button" type="button" [attr.aria-label]="translate('popup.close', selectedLanguage)">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
        </svg>
      </button>
    </div>

    <!-- Popup Body (Empty for now as requested) -->
    <div class="popup-body">
      <ng-content></ng-content>
    </div>
  </div>
</div>
