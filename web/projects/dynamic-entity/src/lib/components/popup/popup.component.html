<!-- Popup Overlay -->
<div [@popupOverlay] (click)="onOverlayClick()" class="popup-overlay">
  <!-- Popup Content -->
  <div [@popupContent] (click)="onContentClick($event)" class="popup-content">
    <!-- Popup Header -->
    <div class="popup-header">
      <h2 class="popup-title">{{ title }}</h2>
      <button (click)="onClosePopup()" class="popup-close-button" type="button" aria-label="Close popup">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
        </svg>
      </button>
    </div>

    <!-- Popup Body (Empty for now as requested) -->
    <div class="popup-body">
      <ng-content></ng-content>
    </div>
  </div>
</div>
