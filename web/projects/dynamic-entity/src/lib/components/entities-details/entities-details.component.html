<!-- Details Header -->
<div class="details-header">
  <button class="back-button" (click)="onBackClick()" type="button" [attr.aria-label]="translate('details.backToList', selectedLanguage)">
    <i class="fas fa-arrow-left"></i>
  </button>

  <div class="entity-header-info" *ngIf="entity">
    <div class="entity-header-icon">
      <i [class]="entity.icon"></i>
    </div>
    <div class="entity-header-text">
      <h2 class="entity-header-name">{{ translate(entity.nameKey, selectedLanguage) }}</h2>
      <p class="entity-header-fields">
        {{ entity.fieldsCount }}
        {{ entity.fieldsCount === 1 ? translate("general.field", selectedLanguage) : translate("general.fields", selectedLanguage) }}
      </p>
    </div>
  </div>
</div>

<!-- Details Content -->
<div class="details-content">
  <div class="placeholder-content">
    <div class="placeholder-icon">
      <i class="fas fa-cog"></i>
    </div>
    <h3 class="placeholder-title">{{ translate("details.title", selectedLanguage) }}</h3>
    <p class="placeholder-description">
      {{ translate("details.description", selectedLanguage) }}
    </p>
  </div>
</div>
