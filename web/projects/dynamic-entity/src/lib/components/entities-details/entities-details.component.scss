// Details Content
.details-content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

// Placeholder Content
.placeholder-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 2rem;
}

.placeholder-icon {
  width: 64px;
  height: 64px;
  border-radius: 50%;

  background: #f3f4f6;
  color: #9ca3af;

  display: flex;
  align-items: center;
  justify-content: center;

  font-size: 1.5rem;
  margin-bottom: 1rem;
}

.placeholder-title {
  margin: 0 0 0.5rem 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
}

.placeholder-description {
  margin: 0;
  font-size: 0.875rem;
  color: #6b7280;
  max-width: 300px;
}

// Responsive Design
@media (max-width: 768px) {
  .details-header {
    padding-bottom: 0.75rem;
    margin-bottom: 0.75rem;
  }

  .back-button {
    width: 36px;
    height: 36px;
    margin-right: 0.75rem;
  }

  .entity-header-icon {
    width: 40px;
    height: 40px;
    font-size: 1rem;
    margin-right: 0.75rem;
  }

  .entity-header-name {
    font-size: 1.25rem;
  }

  .entity-header-fields {
    font-size: 0.8125rem;
  }

  .placeholder-content {
    padding: 1.5rem;
  }
}
