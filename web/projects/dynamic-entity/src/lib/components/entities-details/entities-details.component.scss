// Details Header
.details-header {
  display: flex;
  align-items: center;
  padding-bottom: 1rem;
  margin-bottom: 1rem;
  border-bottom: 1px solid #e5e7eb;
}

// Back Button
.back-button {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: none;
  
  background: #f3f4f6;
  color: #6b7280;
  
  display: flex;
  align-items: center;
  justify-content: center;
  
  cursor: pointer;
  transition: all 0.2s ease;
  margin-right: 1rem;
  flex-shrink: 0;
  
  &:hover {
    background: #e5e7eb;
    color: #374151;
  }
  
  &:active {
    transform: scale(0.95);
  }
}

// Entity Header Info
.entity-header-info {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 0;
}

.entity-header-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  
  display: flex;
  align-items: center;
  justify-content: center;
  
  font-size: 1.25rem;
  flex-shrink: 0;
  margin-right: 1rem;
}

.entity-header-text {
  flex: 1;
  min-width: 0;
}

.entity-header-name {
  margin: 0 0 0.25rem 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  
  // Truncate long names
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.entity-header-fields {
  margin: 0;
  font-size: 0.875rem;
  color: #6b7280;
}

// Details Content
.details-content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

// Placeholder Content
.placeholder-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 2rem;
}

.placeholder-icon {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  
  background: #f3f4f6;
  color: #9ca3af;
  
  display: flex;
  align-items: center;
  justify-content: center;
  
  font-size: 1.5rem;
  margin-bottom: 1rem;
}

.placeholder-title {
  margin: 0 0 0.5rem 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
}

.placeholder-description {
  margin: 0;
  font-size: 0.875rem;
  color: #6b7280;
  max-width: 300px;
}

// Responsive Design
@media (max-width: 768px) {
  .details-header {
    padding-bottom: 0.75rem;
    margin-bottom: 0.75rem;
  }
  
  .back-button {
    width: 36px;
    height: 36px;
    margin-right: 0.75rem;
  }
  
  .entity-header-icon {
    width: 40px;
    height: 40px;
    font-size: 1rem;
    margin-right: 0.75rem;
  }
  
  .entity-header-name {
    font-size: 1.25rem;
  }
  
  .entity-header-fields {
    font-size: 0.8125rem;
  }
  
  .placeholder-content {
    padding: 1.5rem;
  }
}
