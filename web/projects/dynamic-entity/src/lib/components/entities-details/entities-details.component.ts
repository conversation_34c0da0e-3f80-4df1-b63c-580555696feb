import { Component, EventEmitter, Input, Output } from "@angular/core";
import { Entity } from "../entities-list/entities-list.component";
import { translate, SupportedLanguage } from "../../constants/translations";

@Component({
  selector: "lib-entities-details",
  templateUrl: "./entities-details.component.html",
  styleUrls: ["./entities-details.component.scss"],
})
export class EntitiesDetailsComponent {
  @Input() entity: Entity | null = null;
  @Input() selectedLanguage: SupportedLanguage = "en";
  @Output() backToList = new EventEmitter<void>();

  public translate = translate;

  public onBackClick(): void {
    this.backToList.emit();
  }
}
