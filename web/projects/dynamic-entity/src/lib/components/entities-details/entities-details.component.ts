import { Component, EventEmitter, Input, Output } from "@angular/core";
import { Entity } from "../entities-list/entities-list.component";
import { translationService } from "../../constants/translations";

@Component({
  selector: "lib-entities-details",
  templateUrl: "./entities-details.component.html",
  styleUrls: ["./entities-details.component.scss"],
})
export class EntitiesDetailsComponent {
  @Input() entity: Entity | null = null;
  @Output() backToList = new EventEmitter<void>();
  public translationService = translationService;

  public onBackClick(): void {
    this.backToList.emit();
  }
}
