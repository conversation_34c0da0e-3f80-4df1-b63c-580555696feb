import { Component, EventEmitter, Input, Output } from '@angular/core';
import { Entity } from '../entities-list/entities-list.component';

@Component({
  selector: 'lib-entities-details',
  templateUrl: './entities-details.component.html',
  styleUrls: ['./entities-details.component.scss'],
})
export class EntitiesDetailsComponent {
  @Input() entity: Entity | null = null;
  @Output() backToList = new EventEmitter<void>();

  public onBackClick(): void {
    this.backToList.emit();
  }
}
