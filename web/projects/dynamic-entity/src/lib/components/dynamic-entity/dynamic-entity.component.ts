import { Component } from "@angular/core";
import { Entity } from "../entities-list/entities-list.component";

export type ViewMode = "list" | "details";

@Component({
  selector: "lib-dynamic-entity",
  templateUrl: "./dynamic-entity.component.html",
  styleUrls: ["./dynamic-entity.component.scss"],
})
export class DynamicEntityComponent {
  public isPopupOpen = false;
  public currentView: ViewMode = "list";
  public selectedEntity: Entity | null = null;

  public get popupTitle(): string {
    if (this.currentView === "details" && this.selectedEntity) {
      return this.selectedEntity.name;
    }
    return "Dynamic Entities";
  }

  public onButtonClick(): void {
    this.isPopupOpen = true;
    this.currentView = "list";
    this.selectedEntity = null;
  }

  public onClosePopup(): void {
    this.isPopupOpen = false;
    this.currentView = "list";
    this.selectedEntity = null;
  }

  public onEntitySelected(entity: Entity): void {
    this.selectedEntity = entity;
    this.currentView = "details";
  }

  public onBackToList(): void {
    this.currentView = "list";
    this.selectedEntity = null;
  }
}
