import { Component, Input } from "@angular/core";
import { Entity } from "../entities-list/entities-list.component";
import { translate, SupportedLanguage } from "../../constants/translations";

export type ViewMode = "list" | "details";

@Component({
  selector: "lib-dynamic-entity",
  templateUrl: "./dynamic-entity.component.html",
  styleUrls: ["./dynamic-entity.component.scss"],
})
export class DynamicEntityComponent {
  @Input() selectedLanguage: SupportedLanguage = "en";

  public isPopupOpen = false;
  public currentView: ViewMode = "list";
  public selectedEntity: Entity | null = null;

  public get popupTitle(): string {
    if (this.currentView === "details" && this.selectedEntity) {
      return translate(this.selectedEntity.nameKey, this.selectedLanguage);
    }
    return translate("popup.title", this.selectedLanguage);
  }

  public onButtonClick(): void {
    this.isPopupOpen = true;
    this.currentView = "list";
    this.selectedEntity = null;
  }

  public onClosePopup(): void {
    this.isPopupOpen = false;
    this.currentView = "list";
    this.selectedEntity = null;
  }

  public onEntitySelected(entity: Entity): void {
    this.selectedEntity = entity;
    this.currentView = "details";
  }

  public onBackToList(): void {
    this.currentView = "list";
    this.selectedEntity = null;
  }
}
