import { Component } from "@angular/core";
import { Entity } from "../entities-list/entities-list.component";
import { translationService } from "../../constants/translations";

export type ViewMode = "list" | "details";

@Component({
  selector: "lib-dynamic-entity",
  templateUrl: "./dynamic-entity.component.html",
  styleUrls: ["./dynamic-entity.component.scss"],
})
export class DynamicEntityComponent {
  public isPopupOpen = false;
  public currentView: ViewMode = "list";
  public selectedEntity: Entity | null = null;
  public translationService = translationService;

  public get popupTitle(): string {
    if (this.currentView === "details" && this.selectedEntity) {
      return this.translationService.translate(this.selectedEntity.nameKey);
    }
    return this.translationService.translate("popup.title");
  }

  public onButtonClick(): void {
    this.isPopupOpen = true;
    this.currentView = "list";
    this.selectedEntity = null;
  }

  public onClosePopup(): void {
    this.isPopupOpen = false;
    this.currentView = "list";
    this.selectedEntity = null;
  }

  public onEntitySelected(entity: Entity): void {
    this.selectedEntity = entity;
    this.currentView = "details";
  }

  public onBackToList(): void {
    this.currentView = "list";
    this.selectedEntity = null;
  }

  public switchLanguage(language: "en" | "ar"): void {
    this.translationService.setLanguage(language);
  }
}
