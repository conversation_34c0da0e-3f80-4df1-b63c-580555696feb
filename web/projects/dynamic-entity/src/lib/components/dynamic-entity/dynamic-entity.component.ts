import { Component } from '@angular/core';

@Component({
  selector: 'lib-dynamic-entity',
  templateUrl: './dynamic-entity.component.html',
  styleUrls: ['./dynamic-entity.component.scss'],
})
export class DynamicEntityComponent {
  public isPopupOpen = false;
  public popupTitle = 'Dynamic Entity Popup';

  public onButtonClick(): void {
    this.isPopupOpen = true;
  }

  public onClosePopup(): void {
    this.isPopupOpen = false;
  }
}
