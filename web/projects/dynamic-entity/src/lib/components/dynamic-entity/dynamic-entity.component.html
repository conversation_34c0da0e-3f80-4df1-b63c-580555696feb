<!-- Floating <PERSON>ton Component -->
<lib-floating-button [selectedLanguage]="selectedLanguage" (buttonClick)="onButtonClick()"> </lib-floating-button>

<!-- Popup Component -->
<lib-popup *ngIf="isPopupOpen" [title]="popupTitle" [selectedLanguage]="selectedLanguage" (closePopup)="onClosePopup()">
  <!-- Entities List View -->
  <lib-entities-list *ngIf="currentView === 'list'" [selectedLanguage]="selectedLanguage" (entitySelected)="onEntitySelected($event)"> </lib-entities-list>

  <!-- Entities Details View -->
  <lib-entities-details *ngIf="currentView === 'details'" [entity]="selectedEntity" [selectedLanguage]="selectedLanguage" (backToList)="onBackToList()"> </lib-entities-details>
</lib-popup>
