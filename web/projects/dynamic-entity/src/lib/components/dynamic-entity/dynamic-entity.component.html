<!-- Floating <PERSON><PERSON> Component -->
<lib-floating-button [selectedLanguage]="selectedLanguage" (buttonClick)="onButtonClick()"> </lib-floating-button>

<!-- Popup Component -->
<lib-popup *ngIf="isPopupOpen" [title]="popupTitle" [selectedLanguage]="selectedLanguage" [showBackButton]="showBackButton" [headerIcon]="headerIcon" [headerSubtitle]="headerSubtitle" (closePopup)="onClosePopup()" (backClick)="onBackToList()">
  <!-- Entities List View -->
  <lib-entities-list *ngIf="currentView === 'list'" [selectedLanguage]="selectedLanguage" (entitySelected)="onEntitySelected($event)"> </lib-entities-list>

  <!-- Entities Details View -->
  <lib-entities-details *ngIf="currentView === 'details'" [entity]="selectedEntity" [selectedLanguage]="selectedLanguage"> </lib-entities-details>
</lib-popup>
