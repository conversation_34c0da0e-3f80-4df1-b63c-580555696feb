<!-- Floating <PERSON>ton Component -->
<lib-floating-button (buttonClick)="onButtonClick()"></lib-floating-button>

<!-- Popup Component -->
<lib-popup [isOpen]="isPopupOpen" [title]="popupTitle" (closePopup)="onClosePopup()">
  <!-- Entities List View -->
  <lib-entities-list *ngIf="currentView === 'list'" (entitySelected)="onEntitySelected($event)"> </lib-entities-list>

  <!-- Entities Details View -->
  <lib-entities-details *ngIf="currentView === 'details'" [entity]="selectedEntity" (backToList)="onBackToList()"> </lib-entities-details>
</lib-popup>
