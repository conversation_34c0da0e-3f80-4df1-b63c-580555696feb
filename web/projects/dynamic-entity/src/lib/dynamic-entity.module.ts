import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { DragDropModule } from "@angular/cdk/drag-drop";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { FloatingButtonComponent } from "./components/floating-button/floating-button.component";
import { PopupComponent } from "./components/popup/popup.component";
import { DynamicEntityComponent } from "./components/dynamic-entity/dynamic-entity.component";
import { EntitiesListComponent } from "./components/entities-list/entities-list.component";
import { EntitiesDetailsComponent } from "./components/entities-details/entities-details.component";

@NgModule({
  declarations: [
    FloatingButtonComponent,
    PopupComponent,
    DynamicEntityComponent,
    EntitiesListComponent,
    EntitiesDetailsComponent,
  ],
  imports: [CommonModule, DragDropModule, FormsModule, ReactiveFormsModule],
  exports: [
    FloatingButtonComponent,
    PopupComponent,
    DynamicEntityComponent,
    EntitiesListComponent,
    EntitiesDetailsComponent,
  ],
})
export class DynamicEntityModule {}
