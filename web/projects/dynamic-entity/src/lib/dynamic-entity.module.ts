import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { DragDropModule } from "@angular/cdk/drag-drop";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { FloatingButtonComponent } from "./components/floating-button/floating-button.component";
import { PopupComponent } from "./components/popup/popup.component";
import { DynamicEntityComponent } from "./components/dynamic-entity/dynamic-entity.component";

@NgModule({
  declarations: [
    FloatingButtonComponent,
    PopupComponent,
    DynamicEntityComponent,
  ],
  imports: [CommonModule, DragDropModule, FormsModule, ReactiveFormsModule],
  exports: [FloatingButtonComponent, PopupComponent, DynamicEntityComponent],
})
export class DynamicEntityModule {}
