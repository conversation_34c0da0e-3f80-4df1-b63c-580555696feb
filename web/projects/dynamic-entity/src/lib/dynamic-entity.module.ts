import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { DragDropModule } from "@angular/cdk/drag-drop";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { FloatingButtonPopupComponent } from "./components/floating-button-popup/floating-button-popup.component";

@NgModule({
  declarations: [FloatingButtonPopupComponent],
  imports: [CommonModule, DragDropModule, FormsModule, ReactiveFormsModule],
  exports: [FloatingButtonPopupComponent],
})
export class DynamicEntityModule {}
